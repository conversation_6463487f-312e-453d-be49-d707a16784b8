{"scenarios": {"上位机数据读取失败": {"start": "毫米波雷达上位机数据读取失败", "next": {"condition": "毫米波雷达上位机指示灯是否闪烁", "yes": {"condition": "CAN分析仪蓝色工作指示灯是否闪烁", "yes": {"action": "检查毫米波雷达配置"}, "no": {"condition": "检查CAN分析仪接收端CAN线电压是否正常", "yes": {"action": "检测毫米波雷达CAN线电压", "next": {"action": "检查CAN线通信波形，确定故障", "next": {"action": "维修CAN线"}}}, "no": {"action": "检查毫米波雷达CAN线通信"}}}, "no": {"condition": "检查CAN分析仪电源指示灯是否点亮", "yes": {"action": "确认CAN分析仪电源正常（无后续步骤）"}, "no": {"action": "检查USB供电"}}}}, "联合标定运行出错": {"start": "联合标定运行出错", "next": {"condition": "是否显示故障码", "yes": {"action": "根据故障码选择相关模块", "next": {"condition": "是否为相机相关故障", "yes": {"action": "检查线路连接是否正常", "next": {"condition": "检查线路连接是否正常", "yes": {"action": "检查 init.yaml 文件路径是否正常", "next": {"condition": "检查 init.yaml 文件路径是否正常", "yes": {"action": "检查 init.yaml 文件名称是否正常", "next": {"condition": "检查 init.yaml 文件名称是否正常", "yes": {"action": "检查 init.yaml 文件相关参数", "next": {"condition": "检查相机端口是否正常", "yes": {"action": "校正相机端口"}, "no": {"action": "检查相机分辨率，校正分辨率排除故障"}}}, "no": {"action": "校正文件名称"}}}, "no": {"action": "校正至正确路径"}}}, "no": {"action": "调整维修线路"}}}, "no": {"condition": "是否为毫米波雷达相关故障", "yes": {"action": "检查毫米波雷达端口文件"}, "no": {"condition": "是否为激光雷达相关故障", "yes": {"action": "检查激光雷达端口文件"}, "no": {"action": "（无后续分支，流程结束）"}}}}}, "no": {"condition": "检查以太网 IP 是否正确", "yes": {"action": "检查毫米波雷达通道是否正常", "next": {"condition": "检查毫米波雷达通道是否正常", "yes": {"action": "（无后续分支，流程结束）"}, "no": {"action": "校正通道"}}}, "no": {"action": "校正 IP"}}}}, "你好": {"start": "你好，我是故障诊断助手"}}}